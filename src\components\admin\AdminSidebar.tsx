'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import {
  LayoutDashboard,
  Wrench,
  Activity,
  Bot,
  Edit,
  BarChart3,
  Settings,
  ChevronDown,
  ChevronRight,
  X,
  Home,
  Queue,
  FileText,
  Zap
} from 'lucide-react';

interface AdminSidebarProps {
  isOpen: boolean;
  onClose: () => void;
  currentPath: string;
}

interface NavigationItem {
  id: string;
  label: string;
  icon: React.ComponentType<{ size?: number; className?: string }>;
  path: string;
  children?: NavigationItem[];
}

const ADMIN_NAVIGATION: NavigationItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: LayoutDashboard,
    path: '/admin',
    children: []
  },
  {
    id: 'tools',
    label: 'Tool Management',
    icon: Wrench,
    path: '/admin/tools',
    children: [
      { id: 'tools-all', label: 'All Tools', icon: Wrench, path: '/admin' },
      { id: 'tools-new', label: 'Add New Tool', icon: Wrench, path: '/admin/tools/new' },
      { id: 'tools-bulk', label: 'Bulk Import', icon: Wrench, path: '/admin/bulk' },
      { id: 'tools-categories', label: 'Categories', icon: Wrench, path: '/admin/categories' }
    ]
  },
  {
    id: 'jobs',
    label: 'Job Monitoring',
    icon: Activity,
    path: '/admin/jobs',
    children: [
      { id: 'jobs-active', label: 'Active Jobs', icon: Activity, path: '/admin/jobs' },
      { id: 'jobs-performance', label: 'Performance', icon: Activity, path: '/admin/performance' }
    ]
  },
  {
    id: 'content',
    label: 'Content Generation',
    icon: Bot,
    path: '/admin/content',
    children: [
      { id: 'content-config', label: 'AI Configuration', icon: Settings, path: '/admin/content/ai-config' },
      { id: 'content-queue', label: 'Generation Queue', icon: Queue, path: '/admin/content/queue' },
      { id: 'content-review', label: 'Content Review', icon: FileText, path: '/admin/content/review' },
      { id: 'content-prompts', label: 'Prompt Management', icon: Zap, path: '/admin/content/prompts' }
    ]
  },
  {
    id: 'editorial',
    label: 'Editorial Control',
    icon: Edit,
    path: '/admin/editorial',
    children: [
      { id: 'editorial-review', label: 'Review Queue', icon: Edit, path: '/admin/editorial' }
    ]
  },
  {
    id: 'analytics',
    label: 'Analytics',
    icon: BarChart3,
    path: '/admin/analytics',
    children: [
      { id: 'analytics-dashboard', label: 'Analytics Dashboard', icon: BarChart3, path: '/admin/analytics' },
      { id: 'analytics-performance', label: 'Performance Metrics', icon: Activity, path: '/admin/performance' }
    ]
  },
  {
    id: 'settings',
    label: 'System Settings',
    icon: Settings,
    path: '/admin/settings',
    children: []
  }
];

export function AdminSidebar({ isOpen, onClose, currentPath }: AdminSidebarProps) {
  const router = useRouter();
  const [expandedItems, setExpandedItems] = useState<string[]>(['tools', 'jobs']);

  const toggleExpanded = (itemId: string) => {
    setExpandedItems(prev => 
      prev.includes(itemId) 
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  const isActive = (path: string) => {
    if (path === '/admin') {
      return currentPath === '/admin';
    }
    return currentPath.startsWith(path);
  };

  const isParentActive = (item: NavigationItem) => {
    if (isActive(item.path)) return true;
    return item.children?.some(child => isActive(child.path)) || false;
  };

  const handleNavigation = (path: string) => {
    router.push(path);
    onClose(); // Close sidebar on mobile after navigation
  };

  return (
    <>
      {/* Sidebar */}
      <aside
        className={`
          admin-sidebar bg-zinc-800 border-r border-black flex flex-col
          ${isOpen ? 'open' : ''}
        `}
      >
        {/* Sidebar Header */}
        <div className="flex items-center justify-between p-4 border-b border-zinc-700">
          <div className="flex items-center space-x-3">
            <div 
              className="w-8 h-8 rounded-lg flex items-center justify-center text-white font-bold"
              style={{ backgroundColor: 'rgb(255, 150, 0)' }}
            >
              AI
            </div>
            <h2 className="text-lg font-bold text-white">Admin Panel</h2>
          </div>
          
          {/* Close button (mobile only) */}
          <button
            onClick={onClose}
            className="lg:hidden text-gray-400 hover:text-white transition-colors"
          >
            <X size={20} />
          </button>
        </div>

        {/* Navigation */}
        <nav className="flex-1 overflow-y-auto p-4">
          {/* Return to Site */}
          <div className="mb-6">
            <button
              onClick={() => handleNavigation('/')}
              className="w-full flex items-center space-x-3 px-3 py-2 text-gray-300 hover:text-white hover:bg-zinc-700 rounded-lg transition-all duration-200"
            >
              <Home size={18} />
              <span className="text-sm font-medium">Return to Site</span>
            </button>
          </div>

          {/* Navigation Items */}
          <ul className="space-y-2">
            {ADMIN_NAVIGATION.map((item) => (
              <li key={item.id}>
                {/* Parent Item */}
                <div className="flex items-center">
                  <button
                    onClick={() => handleNavigation(item.path)}
                    className={`
                      flex-1 flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200
                      ${isParentActive(item)
                        ? 'text-white'
                        : 'text-gray-300 hover:text-white hover:bg-zinc-700'
                      }
                    `}
                    style={isParentActive(item) ? {
                      backgroundColor: 'rgb(255, 150, 0)'
                    } : {}}
                    onMouseEnter={isParentActive(item) ? (e) => {
                      e.currentTarget.style.backgroundColor = 'rgb(255, 170, 30)';
                    } : undefined}
                    onMouseLeave={isParentActive(item) ? (e) => {
                      e.currentTarget.style.backgroundColor = 'rgb(255, 150, 0)';
                    } : undefined}
                  >
                    <item.icon size={18} />
                    <span>{item.label}</span>
                  </button>
                  
                  {/* Expand/Collapse Button */}
                  {item.children && item.children.length > 0 && (
                    <button
                      onClick={() => toggleExpanded(item.id)}
                      className="p-1 text-gray-400 hover:text-white transition-colors"
                    >
                      {expandedItems.includes(item.id) ? (
                        <ChevronDown size={16} />
                      ) : (
                        <ChevronRight size={16} />
                      )}
                    </button>
                  )}
                </div>

                {/* Children Items */}
                {item.children && item.children.length > 0 && expandedItems.includes(item.id) && (
                  <ul className="mt-2 ml-6 space-y-1">
                    {item.children.map((child) => (
                      <li key={child.id}>
                        <button
                          onClick={() => handleNavigation(child.path)}
                          className={`
                            w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-sm transition-all duration-200
                            ${isActive(child.path)
                              ? 'text-white bg-zinc-700'
                              : 'text-gray-400 hover:text-white hover:bg-zinc-700'
                            }
                          `}
                        >
                          <div className="w-2 h-2 rounded-full bg-gray-500" />
                          <span>{child.label}</span>
                        </button>
                      </li>
                    ))}
                  </ul>
                )}
              </li>
            ))}
          </ul>
        </nav>

        {/* Sidebar Footer */}
        <div className="p-4 border-t border-zinc-700">
          <div className="text-xs text-gray-400 text-center">
            <p>Version 1.0.0</p>
            <p>System Online</p>
          </div>
        </div>
      </aside>
    </>
  );
}
